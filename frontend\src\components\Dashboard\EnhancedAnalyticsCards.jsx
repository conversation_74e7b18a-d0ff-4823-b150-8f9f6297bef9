import { FiUsers, FiDollarSign, FiCalendar, FiShoppingBag, FiTrendingUp, FiTrendingDown, FiClock } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'

const EnhancedAnalyticsCards = ({ dashboardStats }) => {
  const { branding } = useBranding()

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0)
  }

  const formatGrowth = (growth) => {
    if (!growth || growth === 0) return '0%'
    return `${growth > 0 ? '+' : ''}${growth}%`
  }

  const getGrowthColor = (growth) => {
    if (!growth || growth === 0) return 'text-gray-600'
    return growth > 0 ? 'text-green-600' : 'text-red-600'
  }

  const getGrowthIcon = (growth) => {
    if (!growth || growth === 0) return null
    return growth > 0 ? <FiTrendingUp className="w-3 h-3" /> : <FiTrendingDown className="w-3 h-3" />
  }

  const AnalyticsCard = ({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    iconColor, 
    bgGradient, 
    growth, 
    growthLabel,
    onClick 
  }) => (
    <div 
      className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm text-gray-600 font-medium mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mb-2">{subtitle}</p>
          )}
          {growth !== undefined && (
            <div className={`flex items-center space-x-1 text-xs font-medium ${getGrowthColor(growth)}`}>
              {getGrowthIcon(growth)}
              <span>{formatGrowth(growth)}</span>
              {growthLabel && <span className="text-gray-500">{growthLabel}</span>}
            </div>
          )}
        </div>
        <div className="p-3 rounded-xl shadow-lg" style={{ background: bgGradient }}>
          <Icon className="w-6 h-6" style={{ color: iconColor }} />
        </div>
      </div>
    </div>
  )

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <AnalyticsCard
        title="Total Customers"
        value={dashboardStats?.overview?.totalUsers?.toLocaleString() || '0'}
        subtitle={`${dashboardStats?.customers?.newThisMonth || 0} new this month`}
        icon={FiUsers}
        iconColor={branding.colors.primary}
        bgGradient={`linear-gradient(135deg, ${branding.colors.primary}20, ${branding.colors.secondary}20)`}
        growth={dashboardStats?.customers?.growth}
        growthLabel="vs last month"
      />

      <AnalyticsCard
        title="Monthly Revenue"
        value={formatCurrency(dashboardStats?.revenue?.monthly)}
        subtitle={`${formatCurrency(dashboardStats?.revenue?.yearly)} this year`}
        icon={FiDollarSign}
        iconColor="#f3d016"
        bgGradient="linear-gradient(135deg, #f3d01620, #f3d01620)"
        growth={dashboardStats?.revenue?.growth}
        growthLabel="vs last month"
      />

      <AnalyticsCard
        title="Today's Appointments"
        value={dashboardStats?.today?.appointments || 0}
        subtitle={`${dashboardStats?.pending?.appointments || 0} pending approval`}
        icon={FiCalendar}
        iconColor="#8b5cf6"
        bgGradient="linear-gradient(135deg, #8b5cf620, #a855f720)"
      />

      <AnalyticsCard
        title="Tomorrow's Schedule"
        value={dashboardStats?.tomorrow?.appointments || 0}
        subtitle={`${dashboardStats?.week?.appointments || 0} this week total`}
        icon={FiClock}
        iconColor="#06b6d4"
        bgGradient="linear-gradient(135deg, #06b6d420, #0891b220)"
      />

      <AnalyticsCard
        title="Total Products"
        value={dashboardStats?.overview?.totalProducts || 0}
        subtitle="Active products in catalog"
        icon={FiShoppingBag}
        iconColor="#10b981"
        bgGradient="linear-gradient(135deg, #10b98120, #059f6920)"
      />

      <AnalyticsCard
        title="Total Services"
        value={dashboardStats?.overview?.totalServices || 0}
        subtitle="Available service offerings"
        icon={FiUsers}
        iconColor="#f59e0b"
        bgGradient="linear-gradient(135deg, #f59e0b20, #d97f0620)"
      />

      <AnalyticsCard
        title="Processing Orders"
        value={dashboardStats?.pending?.orders || 0}
        subtitle={`${dashboardStats?.overview?.totalOrders || 0} total orders`}
        icon={FiShoppingBag}
        iconColor="#ef4444"
        bgGradient="linear-gradient(135deg, #ef444420, #dc262620)"
      />

      <AnalyticsCard
        title="Today's Orders"
        value={dashboardStats?.today?.orders || 0}
        subtitle="New orders received today"
        icon={FiDollarSign}
        iconColor="#8b5cf6"
        bgGradient="linear-gradient(135deg, #8b5cf620, #a855f720)"
      />
    </div>
  )
}

export default EnhancedAnalyticsCards
