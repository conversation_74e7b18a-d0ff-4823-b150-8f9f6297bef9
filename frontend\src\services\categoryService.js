import apiService from './apiService';

class CategoryService {
  /**
   * Get all categories
   */
  async getCategories(type = null, active = true) {
    try {
      const params = new URLSearchParams();
      if (type) params.append('type', type);
      if (active !== null) params.append('active', active.toString());
      
      const queryString = params.toString();
      const url = `/categories${queryString ? `?${queryString}` : ''}`;
      
      const response = await apiService.get(url);
      return response.data || [];
    } catch (error) {
      console.error('Get categories error:', error);
      throw error;
    }
  }

  /**
   * Get product categories
   */
  async getProductCategories() {
    try {
      const response = await apiService.get('/categories/products');
      return response.data || [];
    } catch (error) {
      console.error('Get product categories error:', error);
      throw error;
    }
  }

  /**
   * Get service categories
   */
  async getServiceCategories() {
    try {
      const response = await apiService.get('/categories/services');
      return response.data || [];
    } catch (error) {
      console.error('Get service categories error:', error);
      throw error;
    }
  }

  /**
   * Get category by ID
   */
  async getCategoryById(id) {
    try {
      const response = await apiService.get(`/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get category error:', error);
      throw error;
    }
  }

  /**
   * Create new category (Admin)
   */
  async createCategory(categoryData) {
    try {
      const response = await apiService.post('/categories', categoryData);
      return response.data;
    } catch (error) {
      console.error('Create category error:', error);
      throw error;
    }
  }

  /**
   * Update category (Admin)
   */
  async updateCategory(id, categoryData) {
    try {
      const response = await apiService.put(`/categories/${id}`, categoryData);
      return response.data;
    } catch (error) {
      console.error('Update category error:', error);
      throw error;
    }
  }

  /**
   * Delete category (Admin)
   */
  async deleteCategory(id) {
    try {
      const response = await apiService.delete(`/categories/${id}`);
      return response;
    } catch (error) {
      console.error('Delete category error:', error);
      throw error;
    }
  }
}

export default new CategoryService();
