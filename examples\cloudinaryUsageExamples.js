/**
 * Cloudinary Upload Service Usage Examples
 * 
 * This file contains comprehensive examples of how to use the Cloudinary upload service
 * in different parts of your application.
 */

// ===== FRONTEND EXAMPLES =====

// Example 1: Basic image upload in a React component
const ImageUploadComponent = () => {
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  const handleUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploading(true);
    try {
      // Upload to Cloudinary with specific type
      const response = await uploadService.uploadImageToCloudinary(file, 'profilePicture');
      
      if (response.success) {
        setImageUrl(response.data.url);
        console.log('Upload successful:', response.data);
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input type="file" onChange={handleUpload} accept="image/*" />
      {uploading && <p>Uploading to cloud storage...</p>}
      {imageUrl && <img src={imageUrl} alt="Uploaded" style={{maxWidth: '200px'}} />}
    </div>
  );
};

// Example 2: Multiple image upload with progress
const MultipleImageUpload = () => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadedUrls, setUploadedUrls] = useState([]);

  const handleMultipleUpload = async () => {
    if (files.length === 0) return;

    setUploading(true);
    try {
      const response = await uploadService.uploadImagesToCloudinary(files, 'productImage');
      
      if (response.success) {
        setUploadedUrls(response.data.imageUrls);
        console.log('Multiple upload successful:', response.data);
      }
    } catch (error) {
      console.error('Multiple upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input 
        type="file" 
        multiple 
        onChange={(e) => setFiles(Array.from(e.target.files))}
        accept="image/*" 
      />
      <button onClick={handleMultipleUpload} disabled={uploading}>
        {uploading ? 'Uploading...' : 'Upload Images'}
      </button>
      
      {uploadedUrls.length > 0 && (
        <div>
          {uploadedUrls.map((url, index) => (
            <img key={index} src={url} alt={`Uploaded ${index}`} style={{width: '100px', margin: '5px'}} />
          ))}
        </div>
      )}
    </div>
  );
};

// Example 3: Specialized upload functions
const SpecializedUploads = () => {
  // Logo upload
  const uploadLogo = async (file) => {
    try {
      const response = await uploadService.uploadLogoToCloudinary(file);
      if (response.success) {
        console.log('Logo uploaded:', response.data.imageUrl);
        return response.data.imageUrl;
      }
    } catch (error) {
      console.error('Logo upload failed:', error);
    }
  };

  // Hero image upload
  const uploadHeroImage = async (file) => {
    try {
      const response = await uploadService.uploadHeroImageToCloudinary(file);
      if (response.success) {
        console.log('Hero image uploaded:', response.data.imageUrl);
        return response.data.imageUrl;
      }
    } catch (error) {
      console.error('Hero image upload failed:', error);
    }
  };

  // Profile picture upload
  const uploadProfilePicture = async (file) => {
    try {
      const response = await uploadService.uploadProfilePictureToCloudinary(file);
      if (response.success) {
        console.log('Profile picture uploaded:', response.data.imageUrl);
        return response.data.imageUrl;
      }
    } catch (error) {
      console.error('Profile picture upload failed:', error);
    }
  };

  return { uploadLogo, uploadHeroImage, uploadProfilePicture };
};

// Example 4: Image optimization
const ImageOptimization = () => {
  const getOptimizedImage = async (imageUrl, options = {}) => {
    try {
      const response = await uploadService.getOptimizedImageUrl(imageUrl, {
        width: 300,
        height: 200,
        quality: 'auto',
        format: 'webp',
        crop: 'fill',
        ...options
      });
      
      if (response.success) {
        return response.data.optimizedUrl;
      }
    } catch (error) {
      console.error('Image optimization failed:', error);
    }
  };

  return { getOptimizedImage };
};

// Example 5: Delete image
const ImageDeletion = () => {
  const deleteImage = async (imageUrl) => {
    try {
      const response = await uploadService.deleteImageFromCloudinary(imageUrl);
      if (response.success) {
        console.log('Image deleted successfully');
        return true;
      }
    } catch (error) {
      console.error('Image deletion failed:', error);
      return false;
    }
  };

  return { deleteImage };
};

// ===== BACKEND EXAMPLES =====

// Example 6: Custom controller using Cloudinary service
const CustomController = {
  // Upload user avatar
  async uploadUserAvatar(req, res) {
    try {
      const imageUrl = await uploadSingleFile(req, 'profilePicture');
      
      if (!imageUrl) {
        return sendError(res, 'Failed to upload avatar');
      }

      // Update user profile with new avatar URL
      const userId = req.user.id;
      await User.findByIdAndUpdate(userId, { avatar: imageUrl });

      sendSuccess(res, 'Avatar uploaded successfully', { imageUrl });
    } catch (error) {
      console.error('Upload avatar error:', error);
      sendError(res, error.message);
    }
  },

  // Upload product gallery
  async uploadProductGallery(req, res) {
    try {
      const imageUrls = await uploadMultipleFiles(req, 'productImage');
      
      if (imageUrls.length === 0) {
        return sendError(res, 'Failed to upload product images');
      }

      // Update product with new gallery images
      const productId = req.params.productId;
      await Product.findByIdAndUpdate(productId, { 
        $push: { gallery: { $each: imageUrls } }
      });

      sendSuccess(res, 'Product gallery updated', { imageUrls });
    } catch (error) {
      console.error('Upload product gallery error:', error);
      sendError(res, error.message);
    }
  }
};

// Example 7: Middleware for automatic image processing
const imageProcessingMiddleware = async (req, res, next) => {
  try {
    if (req.file || req.files) {
      // Automatically determine upload type based on route
      let uploadType = 'galleryImage';
      
      if (req.route.path.includes('profile')) {
        uploadType = 'profilePicture';
      } else if (req.route.path.includes('product')) {
        uploadType = 'productImage';
      } else if (req.route.path.includes('branding')) {
        uploadType = 'brandingImage';
      }

      // Store upload type in request for use in controller
      req.uploadType = uploadType;
    }
    
    next();
  } catch (error) {
    console.error('Image processing middleware error:', error);
    sendError(res, 'Image processing failed');
  }
};

// Example 8: Bulk image operations
const BulkImageOperations = {
  // Upload multiple images with different types
  async uploadMixedImages(files, typeMapping) {
    const results = [];
    
    for (const file of files) {
      try {
        const uploadType = typeMapping[file.fieldname] || 'galleryImage';
        const imageUrl = await uploadToCloudinary(file.path, uploadType);
        results.push({ file: file.originalname, url: imageUrl, type: uploadType });
      } catch (error) {
        console.error(`Failed to upload ${file.originalname}:`, error);
        results.push({ file: file.originalname, error: error.message });
      }
    }
    
    return results;
  },

  // Delete multiple images
  async deleteMultipleImages(imageUrls) {
    const results = [];
    
    for (const imageUrl of imageUrls) {
      try {
        const deleted = await deleteFromCloudinary(imageUrl);
        results.push({ url: imageUrl, deleted });
      } catch (error) {
        console.error(`Failed to delete ${imageUrl}:`, error);
        results.push({ url: imageUrl, error: error.message });
      }
    }
    
    return results;
  }
};

// ===== CONFIGURATION EXAMPLES =====

// Example 9: Environment setup
const environmentSetup = `
# Add these to your .env file:
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key  
CLOUDINARY_API_SECRET=your_api_secret

# Optional: Custom upload settings
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=uploads/temp/
`;

// Example 10: Error handling patterns
const ErrorHandlingPatterns = {
  // Graceful fallback to local storage
  async uploadWithFallback(file, uploadType) {
    try {
      // Try Cloudinary first
      return await uploadService.uploadImageToCloudinary(file, uploadType);
    } catch (cloudinaryError) {
      console.warn('Cloudinary upload failed, using local storage:', cloudinaryError);
      
      try {
        // Fallback to local upload
        return await uploadService.uploadImage(file);
      } catch (localError) {
        console.error('Both upload methods failed:', localError);
        throw new Error('Upload failed completely');
      }
    }
  },

  // Retry mechanism
  async uploadWithRetry(file, uploadType, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await uploadService.uploadImageToCloudinary(file, uploadType);
      } catch (error) {
        console.warn(`Upload attempt ${attempt} failed:`, error);
        
        if (attempt === maxRetries) {
          throw new Error(`Upload failed after ${maxRetries} attempts`);
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }
};

export {
  ImageUploadComponent,
  MultipleImageUpload,
  SpecializedUploads,
  ImageOptimization,
  ImageDeletion,
  CustomController,
  imageProcessingMiddleware,
  BulkImageOperations,
  ErrorHandlingPatterns
};
