import { useState, useEffect } from 'react'
import { FiCalendar, FiShoppingBag, FiHeart, FiUser, FiClock, FiCheckCircle } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'

const UserOverview = ({ 
  userData, 
  appointments, 
  orders, 
  favoriteProducts, 
  sectionLoading 
}) => {
  const { branding } = useBranding()

  const upcomingAppointments = appointments.filter(apt => 
    apt.status !== 'completed' && apt.status !== 'cancelled'
  )

  const recentOrders = orders.slice(0, 3)

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="relative overflow-hidden rounded-2xl p-8 shadow-lg border border-white/20"
           style={{
             background: `linear-gradient(135deg, ${branding.colors.primary}90, ${branding.colors.secondary}80, ${branding.colors.accent}70)`,
             backgroundSize: '400% 400%',
             animation: 'gradientShift 8s ease infinite'
           }}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-2">Welcome back, {(userData.name || userData.firstName || 'User').replace(/\s+User!?$/i, '')}!</h2>
          <p className="text-white/80 text-lg">Here's your personal dashboard overview</p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-medium">Total Appointments</p>
              <p className="text-3xl font-bold text-gray-900">{appointments.length}</p>
              <p className="text-xs text-blue-600 mt-1">
                {upcomingAppointments.length} upcoming
              </p>
            </div>
            <div className="p-3 rounded-xl shadow-lg"
                 style={{ background: `linear-gradient(135deg, ${branding.colors.primary}20, ${branding.colors.secondary}20)` }}>
              <FiCalendar className="w-6 h-6" style={{ color: branding.colors.secondary }} />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-medium">Total Orders</p>
              <p className="text-3xl font-bold text-gray-900">{orders.length}</p>
              <p className="text-xs text-green-600 mt-1">
                ${orders.reduce((sum, order) => sum + (order.total || 0), 0).toFixed(2)} spent
              </p>
            </div>
            <div className="p-3 rounded-xl shadow-lg"
                 style={{ background: `linear-gradient(135deg, #ea580c20, #f97316 20)` }}>
              <FiShoppingBag className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-medium">Favorite Products</p>
              <p className="text-3xl font-bold text-gray-900">{favoriteProducts.length}</p>
              <p className="text-xs text-pink-600 mt-1">
                Items saved
              </p>
            </div>
            <div className="p-3 rounded-xl shadow-lg"
                 style={{ background: `linear-gradient(135deg, #ec489920, #f43f5e20)` }}>
              <FiHeart className="w-6 h-6 text-pink-600" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 font-medium">Member Since</p>
              <p className="text-lg font-bold text-gray-900">
                {userData.joinDate ? new Date(userData.joinDate).getFullYear() : 'Recently'}
              </p>
              <p className="text-xs text-purple-600 mt-1">
                Valued customer
              </p>
            </div>
            <div className="p-3 rounded-xl shadow-lg"
                 style={{ background: `linear-gradient(135deg, #8b5cf620, #a855f720)` }}>
              <FiUser className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Appointments */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Upcoming Appointments</h3>
            <p className="text-gray-600 mt-1">Your scheduled appointments</p>
          </div>
          <button className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
            View All
          </button>
        </div>

        {sectionLoading.appointments ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl animate-pulse">
                <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-32"></div>
                  <div className="h-3 bg-gray-300 rounded w-24"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : upcomingAppointments.length > 0 ? (
          <div className="space-y-4">
            {upcomingAppointments.slice(0, 3).map((appointment, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center"
                       style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}20, ${branding.colors.accent}20)` }}>
                    <FiCalendar className="w-6 h-6" style={{ color: branding.colors.secondary }} />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">{appointment.service || appointment.serviceType || 'Service'}</p>
                    <p className="text-sm text-gray-600">
                      {appointment.date ? new Date(appointment.date).toLocaleDateString() : 'Date not set'}
                    </p>
                    <p className="text-xs text-gray-500">{appointment.time || 'Time not set'}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {appointment.status || 'Pending'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No upcoming appointments</p>
            <button className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer">
              Book Appointment
            </button>
          </div>
        )}
      </div>

      {/* Recent Orders */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Recent Orders</h3>
            <p className="text-gray-600 mt-1">Your latest purchases</p>
          </div>
          <button className="px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
            View All
          </button>
        </div>

        {sectionLoading.orders ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl animate-pulse">
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-32"></div>
                  <div className="h-3 bg-gray-300 rounded w-40"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : recentOrders.length > 0 ? (
          <div className="space-y-4">
            {recentOrders.map((order, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center bg-gradient-to-r from-orange-500 to-orange-600">
                    <FiShoppingBag className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Order #{order.id || `ORD-${index + 1}`}</p>
                    <p className="text-sm text-gray-600">
                      {order.items?.length || 0} items
                    </p>
                    <p className="text-xs text-gray-500">
                      {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Recently'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">${order.total?.toFixed(2) || '0.00'}</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    order.status === 'delivered' ? 'bg-green-100 text-green-800' : 
                    order.status === 'shipped' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {order.status || 'Processing'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiShoppingBag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No orders yet</p>
            <button className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 cursor-pointer">
              Start Shopping
            </button>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <h3 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl hover:from-blue-100 hover:to-blue-200 transition-all duration-200 cursor-pointer group">
            <FiCalendar className="w-8 h-8 text-blue-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-sm font-medium text-blue-900">Book Appointment</span>
          </button>
          
          <button className="flex flex-col items-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl hover:from-green-100 hover:to-green-200 transition-all duration-200 cursor-pointer group">
            <FiShoppingBag className="w-8 h-8 text-green-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-sm font-medium text-green-900">Shop Products</span>
          </button>
          
          <button className="flex flex-col items-center p-4 bg-gradient-to-br from-pink-50 to-pink-100 rounded-xl hover:from-pink-100 hover:to-pink-200 transition-all duration-200 cursor-pointer group">
            <FiHeart className="w-8 h-8 text-pink-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-sm font-medium text-pink-900">View Favorites</span>
          </button>
          
          <button className="flex flex-col items-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-200 cursor-pointer group">
            <FiUser className="w-8 h-8 text-purple-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-sm font-medium text-purple-900">Edit Profile</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default UserOverview
