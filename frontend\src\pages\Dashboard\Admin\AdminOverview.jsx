import { useState, useEffect } from 'react'
import { useBranding } from '../../../contexts/BrandingContext'
import MultiDayAppointments from '../../../components/Dashboard/MultiDayAppointments'
import EnhancedAnalyticsCards from '../../../components/Dashboard/EnhancedAnalyticsCards'

const AdminOverview = ({
  dashboardStats,
  sectionLoading,
  adminData,
  onNavigateToTab
}) => {
  const { branding } = useBranding()

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="relative overflow-hidden rounded-2xl p-8 shadow-lg border border-white/20"
           style={{
             background: `linear-gradient(135deg, ${branding.colors.primary}90, ${branding.colors.secondary}80, ${branding.colors.accent}70)`,
             backgroundSize: '400% 400%',
             animation: 'gradientShift 8s ease infinite'
           }}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-2">Welcome back, {(adminData?.name || adminData?.firstName || 'Admin').replace(/\s+User!?$/i, '')}!</h2>
          <p className="text-white/80 text-lg">Here's your business overview for today</p>
        </div>
      </div>

      {/* Enhanced Analytics Cards */}
      {sectionLoading.overview ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 rounded w-24 mb-2"></div>
                  <div className="h-8 bg-gray-300 rounded w-16 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-20"></div>
                </div>
                <div className="w-12 h-12 bg-gray-300 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <EnhancedAnalyticsCards dashboardStats={dashboardStats} />
      )}

      {/* Multi-Day Appointments */}
      <MultiDayAppointments
        dashboardStats={dashboardStats}
        onNavigateToTab={onNavigateToTab}
      />


    </div>
  )
}

export default AdminOverview
