import { useState, useEffect } from 'react'
import { useBranding } from '../../../contexts/BrandingContext'
import MultiDayAppointments from '../../../components/Dashboard/MultiDayAppointments'
import EnhancedAnalyticsCards from '../../../components/Dashboard/EnhancedAnalyticsCards'

const AdminOverview = ({
  dashboardStats,
  sectionLoading,
  adminData,
  onNavigateToTab
}) => {
  const { branding } = useBranding()

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="relative overflow-hidden rounded-2xl p-8 shadow-lg border border-white/20"
           style={{
             background: `linear-gradient(135deg, ${branding.colors.primary}90, ${branding.colors.secondary}80, ${branding.colors.accent}70)`,
             backgroundSize: '400% 400%',
             animation: 'gradientShift 8s ease infinite'
           }}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-2">Welcome back, {(adminData?.name || adminData?.firstName || 'Admin').replace(/\s+User!?$/i, '')}!</h2>
          <p className="text-white/80 text-lg">Here's your business overview for today</p>
        </div>
      </div>

      {/* Enhanced Analytics Cards */}
      {sectionLoading.overview ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 rounded w-24 mb-2"></div>
                  <div className="h-8 bg-gray-300 rounded w-16 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-20"></div>
                </div>
                <div className="w-12 h-12 bg-gray-300 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <EnhancedAnalyticsCards dashboardStats={dashboardStats} />
      )}

      {/* Multi-Day Appointments */}
      <MultiDayAppointments
        dashboardStats={dashboardStats}
        onNavigateToTab={onNavigateToTab}
      />

      {/* Recent Orders */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Recent Orders</h3>
            <p className="text-gray-600 mt-1">Latest product orders from customers</p>
          </div>
          <button
            onClick={() => onNavigateToTab('orders')}
            className="px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            View All
          </button>
        </div>

        {sectionLoading.orders ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl animate-pulse">
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-32"></div>
                  <div className="h-3 bg-gray-300 rounded w-40"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : orders.length > 0 ? (
          <div className="space-y-4">
            {orders.slice(0, 3).map((order, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer">
                <div>
                  <p className="font-semibold text-gray-900">Order #{order.id || `ORD-${index + 1}`}</p>
                  <p className="text-sm text-gray-600">{order.customer || 'Customer'}</p>
                  <p className="text-xs text-gray-500">
                    {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Date unknown'}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">${order.total?.toFixed(2) || '0.00'}</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${order.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                    {order.status || 'Pending'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiShoppingBag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No recent orders found</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default AdminOverview
