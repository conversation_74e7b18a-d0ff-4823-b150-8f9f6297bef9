import React from 'react'
import { useBranding } from '../../contexts/BrandingContext'

const TermsOfService = () => {
  const { branding, isLoading } = useBranding()

  if (isLoading || !branding) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse text-gray-500">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 
              className="text-4xl font-bold mb-4"
              style={{ color: branding.theme?.colors?.primary || '#008000' }}
            >
              {branding.legal?.termsOfService?.title || 'Terms of Service'}
            </h1>
            <p className="text-gray-600">
              Last updated: {branding.legal?.termsOfService?.lastUpdated 
                ? new Date(branding.legal.termsOfService.lastUpdated).toLocaleDateString()
                : new Date().toLocaleDateString()
              }
            </p>
          </div>

          {/* Content */}
          <div className="prose prose-lg max-w-none">
            {branding.legal?.termsOfService?.content ? (
              <div 
                dangerouslySetInnerHTML={{ 
                  __html: branding.legal.termsOfService.content 
                }}
                className="legal-content"
              />
            ) : (
              <div className="text-gray-600">
                <p>Terms of service content is being updated. Please check back soon.</p>
              </div>
            )}
          </div>

          {/* Back to Home Button */}
          <div className="mt-12 text-center">
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white transition-colors duration-200"
              style={{ 
                backgroundColor: branding.theme?.colors?.primary || '#008000',
                ':hover': { backgroundColor: branding.theme?.colors?.accent || '#006600' }
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = branding.theme?.colors?.accent || '#006600'}
              onMouseLeave={(e) => e.target.style.backgroundColor = branding.theme?.colors?.primary || '#008000'}
            >
              ← Back
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .legal-content h1 {
          color: ${branding.theme?.colors?.primary || '#008000'};
          font-size: 2rem;
          font-weight: bold;
          margin-bottom: 1rem;
        }
        .legal-content h2 {
          color: ${branding.theme?.colors?.primary || '#008000'};
          font-size: 1.5rem;
          font-weight: 600;
          margin-top: 2rem;
          margin-bottom: 1rem;
        }
        .legal-content h3 {
          color: ${branding.theme?.colors?.secondary || '#f3d016'};
          font-size: 1.25rem;
          font-weight: 600;
          margin-top: 1.5rem;
          margin-bottom: 0.75rem;
        }
        .legal-content p {
          margin-bottom: 1rem;
          line-height: 1.6;
        }
        .legal-content ul {
          margin-bottom: 1rem;
          padding-left: 1.5rem;
        }
        .legal-content li {
          margin-bottom: 0.5rem;
        }
      `}</style>
    </div>
  )
}

export default TermsOfService
