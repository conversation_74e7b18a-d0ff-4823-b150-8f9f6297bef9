# Cloudinary Upload Service Documentation

## Overview

This service provides comprehensive file upload functionality using Cloudinary as the cloud storage provider. It supports various upload types, automatic file optimization, and secure file management.

## Features

- ✅ **Cloud Storage**: Files uploaded to Cloudinary for reliable, scalable storage
- ✅ **Multiple Upload Types**: Support for different file categories (profile pictures, products, branding, etc.)
- ✅ **Automatic Optimization**: Built-in image optimization and transformation
- ✅ **Secure Uploads**: Authentication required for all upload operations
- ✅ **File Type Validation**: Supports JPEG, PNG, GIF, WebP, and PDF files
- ✅ **Temporary File Cleanup**: Automatic cleanup of local temporary files
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Legacy Support**: Backward compatibility with existing local upload system

## Setup

### 1. Environment Variables

Add the following to your `.env` file:

```env
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
```

### 2. Cloudinary Account Setup

1. Create a free account at [Cloudinary](https://cloudinary.com/)
2. Get your credentials from the Dashboard
3. Add the credentials to your environment variables

## API Endpoints

### Configuration Check

```http
GET /api/upload/cloudinary/config
```

Checks if Cloudinary is properly configured.

### Single Image Upload

```http
POST /api/upload/cloudinary/image
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "image": <file>,
  "uploadType": "galleryImage" // optional
}
```

### Multiple Images Upload

```http
POST /api/upload/cloudinary/images
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "images": [<file1>, <file2>, ...],
  "uploadType": "productImage" // optional
}
```

### Specialized Upload Endpoints

#### Profile Picture
```http
POST /api/upload/cloudinary/profile-picture
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "profilePicture": <file>
}
```

#### Product Images
```http
POST /api/upload/cloudinary/product-images
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "productImages": [<file1>, <file2>, ...]
}
```

#### Branding Images
```http
POST /api/upload/cloudinary/branding-images
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "brandingImages": [<file1>, <file2>, ...]
}
```

#### Logo Upload
```http
POST /api/upload/cloudinary/logo
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "logo": <file>
}
```

#### Favicon Upload
```http
POST /api/upload/cloudinary/favicon
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "favicon": <file>
}
```

#### Hero Image Upload
```http
POST /api/upload/cloudinary/hero-image
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "heroImage": <file>
}
```

### Image Optimization

```http
GET /api/upload/cloudinary/optimize/:imageUrl?width=300&height=200&quality=auto&format=webp&crop=fill
```

Parameters:
- `width`: Target width in pixels
- `height`: Target height in pixels
- `quality`: Image quality (auto, 100, 80, etc.)
- `format`: Output format (webp, jpg, png, etc.)
- `crop`: Crop mode (fill, fit, scale, etc.)

### Delete Image

```http
DELETE /api/upload/cloudinary/image
Content-Type: application/json
Authorization: Bearer <token>

{
  "imageUrl": "https://res.cloudinary.com/..."
}
```

## Upload Types

The service supports the following upload types, each with its own Cloudinary folder:

| Upload Type | Cloudinary Folder | Use Case |
|-------------|-------------------|----------|
| `profilePicture` | `microlocs/profile_pictures` | User profile images |
| `productImage` | `microlocs/products` | Product catalog images |
| `serviceImage` | `microlocs/services` | Service images |
| `brandingImage` | `microlocs/branding` | General branding assets |
| `testimonialImage` | `microlocs/testimonials` | Customer testimonial images |
| `staffImage` | `microlocs/staff` | Staff member photos |
| `businessDocs` | `microlocs/business_documents` | Business documents (PDF) |
| `logo` | `microlocs/branding/logos` | Company logos |
| `favicon` | `microlocs/branding/favicons` | Website favicons |
| `heroImage` | `microlocs/branding/hero` | Hero/banner images |
| `galleryImage` | `microlocs/gallery` | General gallery images |

## File Limits

- **Maximum file size**: 10MB
- **Supported formats**: JPEG, PNG, GIF, WebP, PDF
- **Multiple upload limit**: 10 files per request

## Response Format

### Success Response

```json
{
  "success": true,
  "message": "Image uploaded successfully to Cloudinary",
  "data": {
    "originalName": "example.jpg",
    "size": 1024000,
    "url": "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/microlocs/gallery/abc123.jpg",
    "uploadType": "galleryImage"
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Invalid file type. Only JPEG, PNG, GIF, WebP and PDF files are allowed.",
  "error": "INVALID_FILE_TYPE"
}
```

## Usage Examples

### Frontend JavaScript

```javascript
// Single image upload
const uploadImage = async (file, uploadType = 'galleryImage') => {
  const formData = new FormData();
  formData.append('image', file);
  formData.append('uploadType', uploadType);

  const response = await fetch('/api/upload/cloudinary/image', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return response.json();
};

// Multiple images upload
const uploadMultipleImages = async (files, uploadType = 'productImage') => {
  const formData = new FormData();
  files.forEach(file => formData.append('images', file));
  formData.append('uploadType', uploadType);

  const response = await fetch('/api/upload/cloudinary/images', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return response.json();
};
```

### React Component Example

```jsx
import React, { useState } from 'react';

const ImageUploader = () => {
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  const handleUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploading(true);
    try {
      const result = await uploadImage(file, 'profilePicture');
      if (result.success) {
        setImageUrl(result.data.url);
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input type="file" onChange={handleUpload} accept="image/*" />
      {uploading && <p>Uploading...</p>}
      {imageUrl && <img src={imageUrl} alt="Uploaded" style={{maxWidth: '200px'}} />}
    </div>
  );
};
```

## Security

- All upload endpoints require authentication
- File type validation prevents malicious uploads
- Temporary files are automatically cleaned up
- Cloudinary provides additional security features

## Error Handling

The service includes comprehensive error handling for:
- Invalid file types
- File size limits
- Cloudinary upload failures
- Authentication errors
- Missing configuration

## Legacy Support

The service maintains backward compatibility with the existing local upload system. Legacy endpoints remain available:
- `POST /api/upload/image`
- `POST /api/upload/images`
- `GET /api/upload/image/:filename`
- `DELETE /api/upload/image/:filename`
