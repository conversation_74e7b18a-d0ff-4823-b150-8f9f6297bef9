{"name": "microlocsbackend", "version": "1.0.0", "description": "TypeScript backend for microlocation services", "main": "dist/server.js", "scripts": {"start": "npm install && tsc && node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "build:clean": "tsc --build --clean && tsc", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "seed": "ts-node src/scripts/seedData.ts", "seed:services": "ts-node src/scripts/seedServices.ts", "check-apis": "echo 'All APIs implemented! Check API_IMPLEMENTATION_STATUS.md for details'", "deploy": "npm run build && npm start"}, "keywords": ["typescript", "express", "mongodb", "mvc"], "author": "Your Name", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.7.0", "cloudinary-build-url": "^0.2.4", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "microlocsbackend": "file:", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}}