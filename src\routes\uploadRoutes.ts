import { Router } from 'express';
import { UploadController } from '../controllers';
import { authenticate } from '../middleware/auth';

const router = Router();

// ===== CLOUDINARY ROUTES =====

// Check Cloudinary configuration
router.get('/cloudinary/config', UploadController.checkCloudinaryConfig);

// POST /api/upload/cloudinary/image - Upload single image to Cloudinary
router.post(
  '/cloudinary/image',
  authenticate,
  UploadController.cloudinaryUploadSingle('image'),
  UploadController.uploadImageToCloudinary
);

// POST /api/upload/cloudinary/images - Upload multiple images to Cloudinary
router.post(
  '/cloudinary/images',
  authenticate,
  UploadController.cloudinaryUploadMultiple('images', 10),
  UploadController.uploadMultipleImagesToCloudinary
);

// DELETE /api/upload/cloudinary/image - Delete image from Cloudinary
router.delete(
  '/cloudinary/image',
  authenticate,
  UploadController.deleteImageFromCloudinary
);

// GET /api/upload/cloudinary/optimize/:imageUrl - Get optimized image URL
router.get(
  '/cloudinary/optimize/:imageUrl',
  UploadController.getOptimizedImage
);

// ===== SPECIALIZED CLOUDINARY UPLOADS =====

// Profile picture upload
router.post(
  '/cloudinary/profile-picture',
  authenticate,
  UploadController.cloudinaryUploadSingle('profilePicture'),
  UploadController.uploadProfilePicture
);

// Product images upload
router.post(
  '/cloudinary/product-images',
  authenticate,
  UploadController.cloudinaryUploadMultiple('productImages', 10),
  UploadController.uploadProductImages
);

// Branding images upload
router.post(
  '/cloudinary/branding-images',
  authenticate,
  UploadController.cloudinaryUploadMultiple('brandingImages', 5),
  UploadController.uploadBrandingImages
);

// Logo upload
router.post(
  '/cloudinary/logo',
  authenticate,
  UploadController.cloudinaryUploadSingle('logo'),
  UploadController.uploadLogo
);

// Favicon upload
router.post(
  '/cloudinary/favicon',
  authenticate,
  UploadController.cloudinaryUploadSingle('favicon'),
  UploadController.uploadFavicon
);

// Hero image upload
router.post(
  '/cloudinary/hero-image',
  authenticate,
  UploadController.cloudinaryUploadSingle('heroImage'),
  UploadController.uploadHeroImage
);

// ===== LEGACY LOCAL ROUTES (for backward compatibility) =====

// POST /api/upload/image
router.post(
  '/image',
  authenticate,
  UploadController.uploadMiddleware,
  UploadController.uploadImage
);

// POST /api/upload/images
router.post(
  '/images',
  authenticate,
  UploadController.uploadMultipleImages
);

// GET /api/upload/image/:filename
router.get(
  '/image/:filename',
  UploadController.getImageInfo
);

// DELETE /api/upload/image/:filename
router.delete(
  '/image/:filename',
  authenticate,
  UploadController.deleteImage
);

export default router;
