
import { FiCalendar, FiShoppingBag, FiStar, FiArrowRight, FiUser, FiSettings } from 'react-icons/fi'
import { useBranding } from '../contexts/BrandingContext'
import OptimizedImage from '../components/OptimizedImage'

const Home = ({ onNavigate }) => {
  const { branding, isLoading } = useBranding()

  // Show loading state when branding is not available
  if (isLoading || !branding) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  const features = [
    {
      icon: <FiCalendar className="w-8 h-8" />,
      title: "Easy Booking",
      description: "Schedule your consultation with just a few clicks"
    },
    {
      icon: <FiStar className="w-8 h-8" />,
      title: "Expert Care",
      description: "10+ years of professional locs and natural hair experience"
    },
    {
      icon: <FiShoppingBag className="w-8 h-8" />,
      title: "Quality Products",
      description: "Premium hair care products for maintaining healthy locs"
    }
  ]

  const services = [
    {
      name: "Micro Locs Installation",
      description: "Precision micro locs for a natural, versatile look",
      price: "Starting at $300",
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      name: "Loc Maintenance",
      description: "Regular maintenance to keep your locs healthy and neat",
      price: "Starting at $80",
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      name: "Consultation",
      description: "Personalized consultation to determine the best loc style for you",
      price: "$50",
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-yellow-50 to-yellow-100 py-20 lg:py-32" aria-label="Hero section with main call-to-action">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
                {branding?.content?.heroTitle || branding?.content?.home?.heroTitle || 'Transform Your Hair with Professional Care'}
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {branding?.content?.heroSubtitle || branding?.content?.home?.heroSubtitle || 'Expert hair services and premium products for your hair journey'}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={() => onNavigate('consultation')}
                  className="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                  style={{ backgroundColor: branding?.colors?.secondary || '#1F2937' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = branding?.colors?.accent || '#F59E0B'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = branding?.colors?.secondary || '#1F2937'}
                  aria-label="Book a consultation appointment"
                >
                  {branding?.content?.buttons?.scheduleConsultation || 'Book Consultation'}
                  <FiCalendar className="ml-2 w-5 h-5" aria-hidden="true" />
                </button>
                <button
                  onClick={() => onNavigate('shop')}
                  className="inline-flex items-center justify-center px-8 py-4 text-lg font-medium bg-white rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                  style={{ color: branding?.colors?.secondary || '#1F2937', borderColor: branding?.colors?.secondary || '#1F2937', borderWidth: '2px' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
                  aria-label="Browse hair care products"
                >
                  {branding?.content?.buttons?.shopNow || 'Shop Products'}
                  <FiShoppingBag className="ml-2 w-5 h-5" aria-hidden="true" />
                </button>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-3xl overflow-hidden shadow-2xl" style={{ background: `linear-gradient(to bottom right, ${branding?.colors?.secondary || '#1F2937'}40, ${branding?.colors?.secondary || '#1F2937'}60)` }}>
                {(branding?.images?.hero || branding?.content?.home?.heroImage) ? (
                  <OptimizedImage
                    src={branding?.images?.hero || branding?.content?.home?.heroImage}
                    alt="Beautiful locs hairstyle showcasing professional loc installation and styling"
                    className="w-full h-full object-cover"
                    lazy={false}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400 text-lg">Hero Image</span>
                  </div>
                )}
              </div>
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-2xl shadow-lg">
                <div className="flex items-center space-x-2">
                  <div className="flex" style={{ color: '#FFFF00' }}>
                    {[...Array(5)].map((_, i) => (
                      <FiStar key={i} className="w-5 h-5 fill-current" />
                    ))}
                  </div>
                  <span className="text-gray-600 font-medium">500+ Happy Clients</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Why Choose {branding?.businessName || branding?.content?.business?.name || 'Us'}?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {branding?.description || branding?.content?.business?.description || 'Professional hair care services with years of experience and dedication to excellence.'}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-8 rounded-2xl bg-gray-50 hover:bg-yellow-50 transition-colors duration-200">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-6" style={{ color: branding?.colors?.secondary || '#1F2937' }}>
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              {branding?.content?.servicesTitle || branding?.content?.services?.pageTitle || 'Our Services'}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {branding?.content?.servicesSubtitle || branding?.content?.services?.pageSubtitle || 'Professional hair care services tailored to your needs'}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-200">
                <div className="aspect-video bg-gray-200">
                  <OptimizedImage
                    src={service.image}
                    alt={`${service.name} - Professional hair service showcasing ${service.description.toLowerCase()}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold" style={{ color: branding?.colors?.primary || '#3B82F6' }}>{service.price}</span>
                    <button
                      onClick={() => onNavigate('consultation')}
                      className="inline-flex items-center font-medium transition-colors duration-200"
                      style={{ color: branding?.colors?.primary || '#3B82F6' }}
                      onMouseEnter={(e) => e.target.style.color = branding?.colors?.accent || '#F59E0B'}
                      onMouseLeave={(e) => e.target.style.color = branding?.colors?.primary || '#3B82F6'}
                    >
                      Book Now
                      <FiArrowRight className="ml-1 w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <button
              onClick={() => onNavigate('services')}
              className="inline-flex items-center px-8 py-4 text-lg font-medium bg-white rounded-lg transition-colors duration-200"
              style={{ color: branding?.colors?.secondary || '#1F2937', borderColor: branding?.colors?.secondary || '#1F2937', borderWidth: '2px' }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
            >
              View All Services
              <FiArrowRight className="ml-2 w-5 h-5" />
            </button>
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20" style={{ backgroundColor: branding?.colors?.secondary || '#1F2937' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Hair?
          </h2>
          <p className="text-xl text-yellow-100 mb-8 max-w-2xl mx-auto">
            Book your consultation today and let's create the perfect locs for your lifestyle
          </p>
          <button
            onClick={() => onNavigate('consultation')}
            className="inline-flex items-center px-8 py-4 text-lg font-medium bg-white rounded-lg transition-colors duration-200"
            style={{ color: branding?.colors?.secondary || '#1F2937' }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
          >
            Schedule Consultation
            <FiCalendar className="ml-2 w-5 h-5" />
          </button>
        </div>
      </section>
    </div>
  )
}

export default Home
